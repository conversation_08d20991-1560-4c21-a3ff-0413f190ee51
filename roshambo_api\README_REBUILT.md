# Roshambo Flask API - Rebuilt from Scratch

This is a completely rebuilt Roshambo Flask API for Reinvent Scoring with comprehensive error handling, GPU management, and try-catch blocks throughout.

## Key Features

### GPU Management
- **Explicitly uses GPU 1 or 2 by default (NOT GPU 0)**
- Auto-detects available GPUs and prefers non-zero GPU IDs
- Comprehensive GPU availability checking
- Fallback to GPU 0 only if no other GPUs available

### Error Handling
- Try-catch blocks wherever needed
- Comprehensive error logging
- Graceful fallbacks for all operations
- Detailed error messages and status reporting

### How Roshambo Works
- Working directory path contains `dataset.smi` and `reference.sdf` files
- Roshambo creates 3 output files in the working directory:
  - `roshambo.csv` - Similarity scores
  - `mols.sdf` - Processed molecules
  - `hits.sdf` - Best hits

## Files

- `app.py` - Main Flask application (rebuilt)
- `start_api.py` - Startup script with environment checks (rebuilt)
- `test_rebuilt_api.py` - Test suite for API validation
- `requirements.txt` - Python dependencies
- `inpdata/` - Working directory for Roshambo operations

## API Endpoints

### Health Check
```
GET /health
```

Returns:
```json
{
  "status": "healthy",
  "service": "roshambo-api",
  "gpu_count": 2,
  "preferred_gpu": 1,
  "roshambo_available": true,
  "working_dir": "/path/to/inpdata"
}
```

### Similarity Calculation
```
POST /similarity
Content-Type: application/json
```

Request payload:
```json
{
  "reference_file": "/path/to/reference.sdf",
  "dataset_file": "/path/to/dataset.smi",
  "ignore_hs": true,
  "n_confs": 0,
  "use_carbon_radii": true,
  "color": true,
  "sort_by": "ComboTanimoto",
  "write_to_file": true,
  "gpu_id": 1,
  "working_dir": "/path/to/working/dir"
}
```

Response:
```json
{
  "success": true,
  "message": "Similarity calculation completed",
  "working_dir": "/path/to/working/dir",
  "output_files": {
    "csv": "/path/to/working/dir/roshambo.csv",
    "mols_sdf": "/path/to/working/dir/mols.sdf",
    "hits_sdf": "/path/to/working/dir/hits.sdf"
  },
  "gpu_used": 1,
  "execution_time": 45.67,
  "file_status": {
    "roshambo.csv": "created (1234 bytes)",
    "mols.sdf": "created (5678 bytes)",
    "hits.sdf": "created (9012 bytes)"
  }
}
```

## Usage

### Starting the Server

```bash
# Method 1: Using the startup script (recommended)
cd roshambo_api
python start_api.py

# Method 2: Direct Flask app
cd roshambo_api
python app.py
```

### Testing the API

```bash
# Run the test suite
cd roshambo_api
python test_rebuilt_api.py
```

### Example API Call

```python
import requests

# Health check
response = requests.get("http://127.0.0.1:5000/health")
print(response.json())

# Similarity calculation
data = {
    "reference_file": "reference.sdf",
    "dataset_file": "dataset.smi",
    "ignore_hs": True,
    "n_confs": 0,
    "use_carbon_radii": True,
    "color": True,
    "sort_by": "ComboTanimoto",
    "write_to_file": True,
    "gpu_id": 1,  # Use GPU 1, not 0
    "working_dir": "inpdata"
}

response = requests.post("http://127.0.0.1:5000/similarity", json=data)
result = response.json()
print(result)
```

## GPU Configuration

The API automatically:
1. Detects available GPUs
2. Prefers GPU 1 or 2 over GPU 0
3. Falls back to GPU 0 only if necessary
4. Reports GPU usage in responses

GPU selection priority:
- If 3+ GPUs available: Use GPU 2
- If 2+ GPUs available: Use GPU 1  
- If only 1 GPU available: Use GPU 0 (with warning)

## Error Handling

All operations are wrapped in try-catch blocks:
- File operations
- GPU detection
- Roshambo API calls
- Directory creation
- Response processing

Errors are logged and returned with appropriate HTTP status codes.

## Security

- Server binds to `127.0.0.1` (localhost only) for security
- Input validation on all parameters
- File path sanitization
- Working directory isolation

## Requirements

- Python 3.7+
- Flask
- Roshambo package
- PyTorch (for GPU detection)
- RDKit (for molecule handling)
- Requests (for testing)

## Installation

```bash
# Install dependencies
pip install flask torch rdkit requests

# Install roshambo (adjust URL as needed)
pip install git+https://github.com/rashatwi/roshambo.git
```

## Troubleshooting

1. **GPU not detected**: Check PyTorch installation and CUDA availability
2. **Roshambo import error**: Ensure roshambo is installed in the correct conda environment
3. **File not found errors**: Check file paths and working directory permissions
4. **Timeout errors**: Increase timeout for large datasets

## Changes from Original

1. **GPU Management**: Explicit preference for GPU 1/2 over GPU 0
2. **Error Handling**: Comprehensive try-catch blocks throughout
3. **Logging**: Enhanced logging with timestamps and levels
4. **Validation**: Better input validation and file checking
5. **Documentation**: Clear documentation of how roshambo works
6. **Testing**: Dedicated test script for validation
7. **Security**: Localhost-only binding by default
