#!/usr/bin/env python3
"""
Test script for the rebuilt Roshambo Flask API.
Tests the API endpoints and validates the GPU setup.
"""

import os
import sys
import requests
import json
import tempfile
from pathlib import Path

def test_health_endpoint():
    """Test the health endpoint."""
    print("🔍 Testing health endpoint...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint working")
            print(f"   Status: {data.get('status')}")
            print(f"   Service: {data.get('service')}")
            print(f"   GPU Count: {data.get('gpu_count')}")
            print(f"   Preferred GPU: {data.get('preferred_gpu')}")
            print(f"   Roshambo Available: {data.get('roshambo_available')}")
            print(f"   Working Dir: {data.get('working_dir')}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server")
        print("   Make sure the server is running: python start_api.py")
        return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def create_test_sdf(filename, smiles_list):
    """Create a test SDF file from SMILES."""
    try:
        from rdkit import Chem
        from rdkit.Chem import AllChem
        
        writer = Chem.SDWriter(filename)
        
        for i, smiles in enumerate(smiles_list):
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                mol = Chem.AddHs(mol)
                AllChem.EmbedMolecule(mol)
                AllChem.MMFFOptimizeMolecule(mol)
                mol.SetProp("_Name", f"mol_{i}")
                writer.write(mol)
        
        writer.close()
        print(f"✅ Created test SDF: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating SDF: {e}")
        return False

def create_test_smi(filename, smiles_list):
    """Create a test SMI file from SMILES."""
    try:
        with open(filename, 'w') as f:
            for i, smiles in enumerate(smiles_list):
                f.write(f"{smiles} mol_{i}\n")
        
        print(f"✅ Created test SMI: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating SMI: {e}")
        return False

def test_similarity_endpoint():
    """Test the similarity endpoint."""
    print("🔍 Testing similarity endpoint...")
    
    # Create test data
    test_dir = "test_data"
    Path(test_dir).mkdir(exist_ok=True)
    
    # Test molecules
    reference_smiles = ["CCO"]  # Ethanol
    dataset_smiles = ["CCO", "CCC", "CCCO", "CC(C)O"]  # Similar alcohols
    
    # Create test files
    ref_file = os.path.join(test_dir, "reference.sdf")
    dataset_file = os.path.join(test_dir, "dataset.smi")
    
    if not create_test_sdf(ref_file, reference_smiles):
        return False
    
    if not create_test_smi(dataset_file, dataset_smiles):
        return False
    
    # Prepare API request
    api_data = {
        "reference_file": ref_file,
        "dataset_file": dataset_file,
        "ignore_hs": True,
        "n_confs": 0,
        "use_carbon_radii": True,
        "color": True,
        "sort_by": "ComboTanimoto",
        "write_to_file": True,
        "gpu_id": 1,  # Use GPU 1 (not 0)
        "working_dir": test_dir
    }
    
    print(f"📤 Sending request to similarity endpoint...")
    print(f"   Reference: {ref_file}")
    print(f"   Dataset: {dataset_file}")
    print(f"   GPU ID: {api_data['gpu_id']}")
    
    try:
        response = requests.post(
            "http://127.0.0.1:5000/similarity",
            json=api_data,
            timeout=300  # 5 minutes timeout
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Similarity endpoint working")
            print(f"   Success: {data.get('success')}")
            print(f"   Message: {data.get('message')}")
            print(f"   GPU Used: {data.get('gpu_used')}")
            print(f"   Execution Time: {data.get('execution_time', 0):.2f}s")
            
            # Check output files
            output_files = data.get('output_files', {})
            for file_type, file_path in output_files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   {file_type}: {file_path} ({file_size} bytes)")
                else:
                    print(f"   {file_type}: {file_path} (NOT FOUND)")
            
            return True
        else:
            print(f"❌ Similarity endpoint failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Similarity endpoint timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server")
        return False
    except Exception as e:
        print(f"❌ Similarity endpoint error: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Testing Rebuilt Roshambo Flask API")
    print("=" * 50)
    
    # Test health endpoint
    if not test_health_endpoint():
        print("❌ Health test failed")
        return False
    
    print()
    
    # Test similarity endpoint
    if not test_similarity_endpoint():
        print("❌ Similarity test failed")
        return False
    
    print()
    print("✅ All tests passed!")
    print("🎉 Rebuilt Roshambo API is working correctly")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
