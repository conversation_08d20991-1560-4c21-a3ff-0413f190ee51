#!/usr/bin/env python3
"""
Startup script for Roshambo Flask API - Rebuilt from Scratch.
This script sets up the environment and starts the Flask API server.
Explicitly uses GPU 1 or 2 by default (not GPU 0).
"""

import os
import sys
from pathlib import Path

def setup_environment():
    """Set up the environment for Roshambo with comprehensive checks."""
    print("🔧 Setting up Roshambo environment...")

    # Create necessary directories with try-catch
    try:
        inpdata_dir = Path("inpdata")
        inpdata_dir.mkdir(exist_ok=True)
        print(f"✅ Created directory: {inpdata_dir}")
    except Exception as e:
        print(f"❌ Failed to create directory: {e}")
        return False

    # Check if roshambo is available with try-catch
    try:
        import roshambo
        print("✅ Roshambo package is available")

        # Try to import the specific API function
        try:
            from roshambo.api import get_similarity_scores
            print("✅ Roshambo API function accessible")
        except ImportError as e:
            print(f"⚠️  Roshambo API function not accessible: {e}")

        return True
    except ImportError as e:
        print(f"❌ Roshambo package not found: {e}")
        print("Please install roshambo in your conda environment:")
        print("  conda activate your_roshambo_env")
        print("  pip install git+https://github.com/rashatwi/roshambo.git")
        return False

def check_gpu():
    """Check GPU availability and show preferred GPU (1 or 2, not 0)."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✅ GPU available: {gpu_count} device(s)")

            # Show all available GPUs
            for i in range(gpu_count):
                try:
                    gpu_name = torch.cuda.get_device_name(i)
                    print(f"  GPU {i}: {gpu_name}")
                except Exception as e:
                    print(f"  GPU {i}: Error getting name - {e}")

            # Determine preferred GPU (avoid GPU 0)
            if gpu_count >= 3:
                preferred_gpu = 2
                print(f"🎯 Preferred GPU: {preferred_gpu} (avoiding GPU 0)")
            elif gpu_count >= 2:
                preferred_gpu = 1
                print(f"🎯 Preferred GPU: {preferred_gpu} (avoiding GPU 0)")
            else:
                preferred_gpu = 0
                print(f"⚠️  Only GPU 0 available, using it as fallback")

            return True, preferred_gpu
        else:
            print("⚠️  No GPU available, will use CPU")
            return False, None
    except ImportError:
        print("⚠️  PyTorch not available, cannot check GPU")
        return False, None
    except Exception as e:
        print(f"❌ Error checking GPU: {e}")
        return False, None

def check_conda_environment():
    """Check if we're in a conda environment."""
    try:
        conda_env = os.environ.get('CONDA_DEFAULT_ENV')
        if conda_env:
            print(f"🐍 Conda environment: {conda_env}")
        else:
            print("⚠️  Not in a conda environment")

        # Check for RDBASE (RDKit environment variable)
        rdbase = os.environ.get('RDBASE')
        if rdbase:
            print(f"🧪 RDBASE: {rdbase}")
        else:
            print("⚠️  RDBASE not set")

    except Exception as e:
        print(f"❌ Error checking environment: {e}")

def start_api(host='127.0.0.1', port=5000, debug=True):
    """Start the Flask API server with try-catch."""
    print(f"🚀 Starting Roshambo Flask API on {host}:{port}")

    try:
        # Import and run the Flask app
        from app import app
        app.run(host=host, port=port, debug=debug)
    except ImportError as e:
        print(f"❌ Failed to import Flask app: {e}")
        raise
    except Exception as e:
        print(f"❌ Failed to start Flask server: {e}")
        raise

def main():
    """Main function with comprehensive setup and error handling."""
    print("🧬 Roshambo Flask API Startup - Rebuilt from Scratch")
    print("=" * 60)

    # Check conda environment
    try:
        check_conda_environment()
    except Exception as e:
        print(f"⚠️  Environment check failed: {e}")

    # Setup environment
    try:
        if not setup_environment():
            print("❌ Environment setup failed")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Critical error in environment setup: {e}")
        sys.exit(1)

    # Check GPU
    try:
        gpu_available, preferred_gpu = check_gpu()
        if gpu_available:
            print(f"✅ GPU setup complete - preferred GPU: {preferred_gpu}")
        else:
            print("⚠️  No GPU available, continuing with CPU")
    except Exception as e:
        print(f"❌ GPU check failed: {e}")

    print("=" * 60)
    print("🚀 Starting Flask API server...")

    # Start API
    try:
        start_api()
    except KeyboardInterrupt:
        print("\n👋 Shutting down Roshambo API")
    except Exception as e:
        print(f"❌ Error starting API: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
