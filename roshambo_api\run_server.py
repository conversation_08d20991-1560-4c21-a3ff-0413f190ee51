#!/usr/bin/env python3
"""
Simple script to run the rebuilt Roshambo Flask API server.
This script provides a clean way to start the server with proper checks.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Main function to start the Roshambo API server."""
    print("🧬 Roshambo Flask API Server Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    current_dir = Path.cwd()
    if not (current_dir / "app.py").exists():
        print("❌ app.py not found in current directory")
        print("   Please run this script from the roshambo_api directory")
        sys.exit(1)
    
    # Check if start_api.py exists
    if not (current_dir / "start_api.py").exists():
        print("❌ start_api.py not found in current directory")
        sys.exit(1)
    
    print("✅ Found required files")
    print(f"📁 Working directory: {current_dir}")
    
    # Try to start the server
    try:
        print("🚀 Starting Roshambo API server...")
        print("   Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Run the start_api.py script
        subprocess.run([sys.executable, "start_api.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
