# DockStream Debugging Guide

## Issues Identified

### 1. ADV and raw_ADV Columns Showing All Zeros

**Root Cause**: The DockStream docker.py script is not returning proper scores to the REINVENT scoring component.

**Possible Reasons**:
- DockStream command execution is failing silently
- Output format from docker.py is not what REINVENT expects
- Environment/path configuration issues
- Missing or incorrect receptor files
- DockStream configuration errors

### 2. Missing scaffold.csv File

**Root Cause**: scaffold.csv is only generated during **reinforcement learning mode**, not scoring mode.

**Key Points**:
- scaffold.csv is created by the diversity filter system
- Only molecules with scores above the `minscore` threshold are included
- If all scores are 0, no molecules will be added to the scaffold memory
- Scoring mode doesn't use diversity filters, so no scaffold.csv is generated

## Debugging Steps

### Step 1: Use the Enhanced DockStream Component

The updated DockStream component now includes:
- Detailed debug logging
- Better error handling
- Command execution diagnostics
- Score parsing validation

### Step 2: Run the Debugging Script

```bash
python debug_dockstream.py \
    --config /home/<USER>/Desktop/Getting Started/protac-invent/DockStream-master/example.json \
    --docker /home/<USER>/Desktop/Getting Started/protac-invent/DockStream-master/docker.py \
    --env /home/<USER>/.conda/envs/DockStream-master
```

This will test:
- Environment accessibility
- Docker script functionality
- Configuration file validity
- Simple docking execution

### Step 3: Check Your Configuration

#### DockStream Configuration Example:
```json
{
    "ligand_preparation": {
        "type": "RDkit",
        "target_ph": 7.4,
        "ph_tolerance": 1.0
    },
    "docking_runs": [{
        "backend": "AutoDockVina",
        "run_id": "test_run",
        "input_pools": ["RDkit_pool"],
        "parameters": {
            "binary_location": "vina",
            "parallelization": {
                "number_cores": 4
            },
            "seed": 42,
            "receptor_pdbqt_path": ["/absolute/path/to/receptor.pdbqt"],
            "number_poses": 2,
            "search_space": {
                "--center_x": 0.0,
                "--center_y": 0.0,
                "--center_z": 0.0,
                "--size_x": 20.0,
                "--size_y": 20.0,
                "--size_z": 20.0
            }
        },
        "output": {
            "poses": {
                "poses_path": "output/poses.sdf",
                "overwrite": true
            },
            "scores": {
                "scores_path": "output/scores.csv",
                "overwrite": true
            }
        }
    }]
}
```

#### REINVENT Configuration Example:
```json
{
    "component_type": "dockstream",
    "name": "ADV",
    "weight": 1.0,
    "specific_parameters": {
        "configuration_path": "/absolute/path/to/dockstream_config.json",
        "docker_script_path": "/absolute/path/to/DockStream/docker.py",
        "environment_path": "/absolute/path/to/python/or/conda/env",
        "debug": true,
        "transformation": {
            "transformation_type": "reverse_sigmoid",
            "low": -12,
            "high": -6,
            "k": 0.5
        }
    }
}
```

### Step 4: Test DockStream Manually

Before using it in REINVENT, test DockStream directly:

```bash
# Test with simple molecules
/path/to/python/env /path/to/docker.py \
    -conf /path/to/config.json \
    -smiles "CCO;CC(=O)O" \
    -print_scores
```

Expected output should be numeric scores, one per line:
```
-5.2
-3.8
```

### Step 5: Check File Paths and Permissions

Ensure all paths are:
- **Absolute paths** (not relative)
- **Accessible** from the REINVENT working directory
- **Executable** (for environment and scripts)
- **Readable** (for configuration and receptor files)

### Step 6: For scaffold.csv Generation

To generate scaffold.csv, you need:

1. **Use reinforcement learning mode** (not scoring mode)
2. **Configure diversity filter** with appropriate minscore
3. **Ensure molecules get non-zero scores**

Example reinforcement learning configuration:
```json
{
    "run_type": "reinforcement_learning",
    "parameters": {
        "diversity_filter": {
            "name": "IdenticalMurckoScaffold",
            "minscore": 0.1,
            "bucket_size": 25
        },
        "scoring_function": {
            "name": "custom_sum",
            "parameters": [
                {
                    "component_type": "dockstream",
                    "name": "ADV",
                    "weight": 1.0,
                    "specific_parameters": {
                        "configuration_path": "/path/to/dockstream_config.json",
                        "docker_script_path": "/path/to/docker.py",
                        "environment_path": "/path/to/python/env",
                        "debug": true
                    }
                }
            ]
        }
    }
}
```

## Common Issues and Solutions

### Issue: "Command not found" or "Permission denied"
**Solution**: Check that environment_path points to the correct Python executable and is executable.

### Issue: "Configuration file not found"
**Solution**: Use absolute paths for all file references.

### Issue: "Receptor file not found"
**Solution**: Ensure receptor_pdbqt_path in DockStream config uses absolute paths.

### Issue: All scores are 0 but no errors
**Solution**: 
- Check if AutoDock Vina is properly installed and accessible
- Verify search space coordinates are reasonable
- Test with known good molecules

### Issue: scaffold.csv is empty
**Solution**:
- Lower the diversity filter minscore threshold
- Ensure you're using reinforcement learning mode
- Check that scoring components return non-zero scores

## Monitoring and Logs

With debug mode enabled, you'll see detailed output like:
```
🔧 DockStream Debug Mode Enabled
   Configuration path: /path/to/config.json
   Docker script path: /path/to/docker.py
   Environment path: /path/to/python

🧮 DockStream calculating scores for 10 molecules at step 0
   SMILES: ['CCO', 'CC(=O)O', 'c1ccccc1']...
   Command: /path/to/python /path/to/docker.py -conf /path/to/config.json -smiles "CCO;CC(=O)O;c1ccccc1" -print_scores
   Raw results: ['-5.2', '-3.8', '-4.1']
   Score 0: -5.2 -> -5.2
   Score 1: -3.8 -> -3.8
   Score 2: -4.1 -> -4.1
   Final scores: [-5.2, -3.8, -4.1]
   Transformed scores: [0.85, 0.72, 0.78]
```

This will help you identify exactly where the problem occurs in the scoring pipeline.
