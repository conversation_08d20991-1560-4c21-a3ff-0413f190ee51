"""
Roshambo Flask API for Reinvent Scoring - Rebuilt from Scratch.
This API provides a RESTful interface to the Roshambo shape similarity engine.
Explicitly uses GPU 1 or 2 by default (not GPU 0).
"""

import os
import shutil
import tempfile
import signal
import time
import gc
from pathlib import Path
from flask import Flask, request, jsonify
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
ROSHAMBO_WORKING_DIR = os.path.join(os.path.dirname(__file__), "inpdata")
Path(ROSHAMBO_WORKING_DIR).mkdir(parents=True, exist_ok=True)

def check_gpu_availability():
    """Check GPU availability and return preferred GPU ID (1 or 2, not 0)."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"GPU available: {gpu_count} device(s)")

            # Prefer GPU 1 or 2, avoid GPU 0
            if gpu_count >= 3:
                preferred_gpu = 2
            elif gpu_count >= 2:
                preferred_gpu = 1
            else:
                logger.warning("Only GPU 0 available, using it as fallback")
                preferred_gpu = 0

            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                logger.info(f"  GPU {i}: {gpu_name}")

            return preferred_gpu, gpu_count
        else:
            logger.warning("No GPU available, will use CPU")
            return None, 0
    except ImportError:
        logger.warning("PyTorch not available, cannot check GPU")
        return None, 0

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint with GPU status."""
    try:
        preferred_gpu, gpu_count = check_gpu_availability()

        # Check roshambo availability
        roshambo_available = False
        try:
            from roshambo.api import get_similarity_scores
            roshambo_available = True
        except ImportError as e:
            logger.warning(f"Roshambo not available: {e}")

        return jsonify({
            "status": "healthy",
            "service": "roshambo-api",
            "gpu_count": gpu_count,
            "preferred_gpu": preferred_gpu,
            "roshambo_available": roshambo_available,
            "working_dir": ROSHAMBO_WORKING_DIR
        })
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return jsonify({
            "status": "error",
            "service": "roshambo-api",
            "error": str(e)
        }), 500

@app.route('/similarity', methods=['POST'])
def calculate_similarity():
    """
    Calculate molecular shape similarity using Roshambo.

    How roshambo works:
    - Working directory path contains dataset.smi and reference.sdf file
    - Roshambo creates CSV, mols.sdf, and hits.sdf files in the working directory

    Expected JSON payload:
    {
        "reference_file": "path/to/reference.sdf",
        "dataset_file": "path/to/dataset.smi or dataset.sdf",
        "ignore_hs": true,
        "n_confs": 0,
        "use_carbon_radii": true,
        "color": true,
        "sort_by": "ComboTanimoto",
        "write_to_file": true,
        "gpu_id": 1,  # Default to GPU 1 or 2, not 0
        "working_dir": "path/to/working/dir"
    }
    """
    try:
        # Validate request content type
        if not request.is_json:
            logger.error("Request is not JSON")
            return jsonify({"success": False, "error": "Request must be JSON"}), 400

        data = request.get_json()
        if not data:
            logger.error("No JSON data provided")
            return jsonify({"success": False, "error": "No JSON data provided"}), 400

        logger.info(f"Received similarity request: {data}")

        # Extract and validate required parameters
        try:
            reference_file = data.get("reference_file")
            dataset_file = data.get("dataset_file")
            working_dir = data.get("working_dir", ROSHAMBO_WORKING_DIR)
        except Exception as e:
            logger.error(f"Error extracting parameters: {e}")
            return jsonify({"success": False, "error": f"Error extracting parameters: {e}"}), 400

        # Validate required parameters
        if not reference_file:
            logger.error("reference_file is required")
            return jsonify({"success": False, "error": "reference_file is required"}), 400
        if not dataset_file:
            logger.error("dataset_file is required")
            return jsonify({"success": False, "error": "dataset_file is required"}), 400

        # Ensure working directory exists
        try:
            Path(working_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"Working directory: {working_dir}")
        except Exception as e:
            logger.error(f"Failed to create working directory: {e}")
            return jsonify({"success": False, "error": f"Failed to create working directory: {e}"}), 500

        # Handle file paths - make them absolute if relative to working directory
        try:
            if not os.path.isabs(reference_file):
                reference_file = os.path.join(working_dir, reference_file)
            if not os.path.isabs(dataset_file):
                dataset_file = os.path.join(working_dir, dataset_file)
        except Exception as e:
            logger.error(f"Error processing file paths: {e}")
            return jsonify({"success": False, "error": f"Error processing file paths: {e}"}), 400

        # Validate files exist and are readable
        try:
            if not os.path.exists(reference_file):
                logger.error(f"Reference file not found: {reference_file}")
                return jsonify({"success": False, "error": f"Reference file not found: {reference_file}"}), 400
            if not os.path.exists(dataset_file):
                logger.error(f"Dataset file not found: {dataset_file}")
                return jsonify({"success": False, "error": f"Dataset file not found: {dataset_file}"}), 400
        except Exception as e:
            logger.error(f"Error validating files: {e}")
            return jsonify({"success": False, "error": f"Error validating files: {e}"}), 400

        logger.info(f"Reference file: {reference_file}")
        logger.info(f"Dataset file: {dataset_file}")

        # Determine GPU ID - prefer GPU 1 or 2, not 0
        try:
            requested_gpu = data.get("gpu_id")
            if requested_gpu is None:
                # Auto-select preferred GPU
                preferred_gpu, gpu_count = check_gpu_availability()
                gpu_id = preferred_gpu if preferred_gpu is not None else 0
            else:
                gpu_id = max(0, int(requested_gpu))

            logger.info(f"Using GPU ID: {gpu_id}")
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid GPU ID: {e}")
            return jsonify({"success": False, "error": f"Invalid GPU ID: {e}"}), 400

        # Prepare roshambo parameters
        try:
            roshambo_params = {
                "ref_file": reference_file,
                "dataset_files_pattern": dataset_file,
                "ignore_hs": data.get("ignore_hs", True),
                "n_confs": max(0, int(data.get("n_confs", 0))),
                "use_carbon_radii": data.get("use_carbon_radii", True),
                "color": data.get("color", True),
                "sort_by": data.get("sort_by", "ComboTanimoto"),
                "write_to_file": data.get("write_to_file", True),
                "gpu_id": gpu_id,
                "working_dir": working_dir
            }
            logger.info(f"Roshambo parameters: {roshambo_params}")
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid parameter values: {e}")
            return jsonify({"success": False, "error": f"Invalid parameter values: {e}"}), 400

        # Call roshambo API
        try:
            result = call_roshambo_api(**roshambo_params)
        except Exception as e:
            logger.error(f"Error calling roshambo API: {e}")
            return jsonify({"success": False, "error": f"Roshambo API call failed: {e}"}), 500

        # Process result
        if result.get("success"):
            return jsonify({
                "success": True,
                "message": "Similarity calculation completed",
                "working_dir": working_dir,
                "output_files": {
                    "csv": os.path.join(working_dir, "roshambo.csv"),
                    "mols_sdf": os.path.join(working_dir, "mols.sdf"),
                    "hits_sdf": os.path.join(working_dir, "hits.sdf")
                },
                "gpu_used": gpu_id,
                "file_status": result.get("file_status", {}),
                "execution_time": result.get("execution_time", 0)
            })
        else:
            logger.error(f"Roshambo API failed: {result}")
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"Critical error in similarity calculation: {e}")
        return jsonify({"success": False, "error": f"Critical error: {str(e)}"}), 500

def call_roshambo_api(**kwargs):
    """
    Call the roshambo API with comprehensive error handling and try-catch blocks.

    How roshambo works:
    - Working directory path contains dataset.smi and reference.sdf file
    - Roshambo creates CSV, mols.sdf, and hits.sdf files in the working directory
    """
    original_cwd = None
    start_time = time.time()

    try:
        # Import roshambo with try-catch
        try:
            from roshambo.api import get_similarity_scores
            logger.info("Successfully imported roshambo.api")
        except ImportError as e:
            logger.error(f"Roshambo import error: {e}")
            return {
                "success": False,
                "error": f"Roshambo not available: {e}"
            }

        # Validate working directory
        working_dir = kwargs.get("working_dir", ROSHAMBO_WORKING_DIR)
        if not working_dir:
            logger.error("No working directory specified")
            return {
                "success": False,
                "error": "No working directory specified"
            }

        # Ensure working directory exists
        try:
            Path(working_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"Working directory ready: {working_dir}")
        except Exception as e:
            logger.error(f"Failed to create working directory {working_dir}: {e}")
            return {
                "success": False,
                "error": f"Failed to create working directory: {e}"
            }

        # Change to working directory with try-catch
        original_cwd = os.getcwd()
        try:
            os.chdir(working_dir)
            logger.info(f"Changed to working directory: {working_dir}")
        except Exception as e:
            logger.error(f"Failed to change to working directory {working_dir}: {e}")
            return {
                "success": False,
                "error": f"Failed to change to working directory: {e}"
            }

        # Validate required parameters
        required_params = ["ref_file", "dataset_files_pattern"]
        missing_params = [param for param in required_params if not kwargs.get(param)]
        if missing_params:
            logger.error(f"Missing required parameters: {missing_params}")
            return {
                "success": False,
                "error": f"Missing required parameters: {missing_params}"
            }

        # Validate input files exist in working directory
        try:
            ref_file = kwargs.get("ref_file")
            dataset_file = kwargs.get("dataset_files_pattern")

            # Check if files exist (they should be absolute paths or in working dir)
            if not os.path.isabs(ref_file):
                ref_file_path = os.path.join(working_dir, ref_file)
            else:
                ref_file_path = ref_file

            if not os.path.isabs(dataset_file):
                dataset_file_path = os.path.join(working_dir, dataset_file)
            else:
                dataset_file_path = dataset_file

            if not os.path.exists(ref_file_path):
                logger.error(f"Reference file not found: {ref_file_path}")
                return {
                    "success": False,
                    "error": f"Reference file not found: {ref_file_path}"
                }

            if not os.path.exists(dataset_file_path):
                logger.error(f"Dataset file not found: {dataset_file_path}")
                return {
                    "success": False,
                    "error": f"Dataset file not found: {dataset_file_path}"
                }

            logger.info(f"Input files validated - Ref: {ref_file_path}, Dataset: {dataset_file_path}")

        except Exception as e:
            logger.error(f"Error validating input files: {e}")
            return {
                "success": False,
                "error": f"Error validating input files: {e}"
            }

        logger.info(f"Calling roshambo with parameters: {kwargs}")

        # Call roshambo with timeout and comprehensive try-catch
        def timeout_handler(signum, frame):
            raise TimeoutError("Roshambo execution timed out")

        try:
            # Set adaptive timeout based on dataset size
            base_timeout = 600  # 10 minutes base
            n_confs = kwargs.get("n_confs", 0)
            timeout_seconds = base_timeout + (n_confs * 30)  # Add 30s per conformer

            # Set timeout signal (only on Unix-like systems)
            try:
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout_seconds)
                logger.info(f"Set timeout: {timeout_seconds}s")
            except AttributeError:
                # Windows doesn't support SIGALRM
                logger.info("Timeout signal not available on this platform")

            logger.info(f"Starting roshambo execution...")

            try:
                # Force garbage collection before heavy computation
                gc.collect()

                # Call roshambo (returns None but writes files to working directory)
                get_similarity_scores(**kwargs)

                execution_time = time.time() - start_time
                logger.info(f"Roshambo execution completed in {execution_time:.2f} seconds")

            except Exception as e:
                logger.error(f"Roshambo execution failed: {e}")
                return {
                    "success": False,
                    "error": f"Roshambo execution failed: {e}"
                }
            finally:
                # Always cancel the alarm if it was set
                try:
                    signal.alarm(0)
                except AttributeError:
                    pass  # Windows doesn't support SIGALRM
                # Force garbage collection after computation
                gc.collect()

        except TimeoutError:
            logger.error(f"Roshambo execution timed out after {timeout_seconds} seconds")
            return {
                "success": False,
                "error": f"Roshambo execution timed out after {timeout_seconds} seconds"
            }

        # Check if output files were created with detailed validation
        expected_files = ["roshambo.csv", "mols.sdf", "hits.sdf"]
        created_files = []
        file_status = {}

        for filename in expected_files:
            try:
                file_path = os.path.join(working_dir, filename)
                if os.path.exists(filename):  # Check in current working directory
                    file_size = os.path.getsize(filename)
                    if file_size > 0:
                        created_files.append(filename)
                        file_status[filename] = f"created ({file_size} bytes)"
                        logger.info(f"Created file: {filename} ({file_size} bytes)")
                    else:
                        file_status[filename] = "empty file"
                        logger.warning(f"Created empty file: {filename}")
                else:
                    file_status[filename] = "not created"
                    logger.warning(f"File not created: {filename}")
            except Exception as e:
                file_status[filename] = f"error checking: {e}"
                logger.error(f"Error checking file {filename}: {e}")

        execution_time = time.time() - start_time

        if created_files:
            logger.info(f"Roshambo completed successfully. Created files: {created_files}")
            return {
                "success": True,
                "message": f"Roshambo completed successfully. Created files: {created_files}",
                "created_files": created_files,
                "file_status": file_status,
                "execution_time": execution_time
            }
        else:
            logger.error("Roshambo completed but no valid output files were created")
            return {
                "success": False,
                "error": "Roshambo completed but no valid output files were created",
                "file_status": file_status,
                "execution_time": execution_time
            }

    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Critical error in call_roshambo_api: {e}")
        return {
            "success": False,
            "error": f"Critical error: {e}",
            "execution_time": execution_time
        }
    finally:
        # Always restore original working directory with try-catch
        if original_cwd and os.path.exists(original_cwd):
            try:
                os.chdir(original_cwd)
                logger.info(f"Restored working directory to: {original_cwd}")
            except Exception as e:
                logger.error(f"Failed to restore original working directory: {e}")

def move_roshambo_outputs(target_dir):
    """
    Move roshambo output files to the target directory.
    Roshambo creates mols.sdf, hits.sdf, and roshambo.csv in the working directory.
    """
    try:
        # Validate target directory
        if not target_dir:
            logger.error("No target directory specified for moving output files")
            return

        # Ensure target directory exists
        try:
            Path(target_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create target directory {target_dir}: {e}")
            return

        # Roshambo output files
        output_files = ["mols.sdf", "hits.sdf", "roshambo.csv"]
        moved_files = []
        failed_files = []

        for filename in output_files:
            try:
                # Check if file exists in current directory
                if os.path.exists(filename):
                    target_path = os.path.join(target_dir, filename)

                    # Check if target already exists
                    if os.path.exists(target_path):
                        try:
                            # Compare file sizes to see if we need to update
                            source_size = os.path.getsize(filename)
                            target_size = os.path.getsize(target_path)
                            if source_size != target_size:
                                # Replace with newer file
                                os.remove(target_path)
                                shutil.move(filename, target_path)
                                moved_files.append(filename)
                                logger.info(f"Replaced {filename} in {target_path} (size: {target_size} -> {source_size})")
                            else:
                                logger.info(f"File {filename} already exists in target with same size")
                        except Exception as e:
                            logger.warning(f"Error comparing file sizes for {filename}: {e}")
                            # Try to move anyway
                            try:
                                os.remove(target_path)
                                shutil.move(filename, target_path)
                                moved_files.append(filename)
                                logger.info(f"Replaced {filename} in {target_path}")
                            except Exception as e2:
                                failed_files.append((filename, str(e2)))
                                logger.error(f"Failed to replace {filename}: {e2}")
                    else:
                        # Move file to target
                        try:
                            shutil.move(filename, target_path)
                            moved_files.append(filename)
                            logger.info(f"Moved {filename} to {target_path}")
                        except Exception as e:
                            failed_files.append((filename, str(e)))
                            logger.error(f"Failed to move {filename}: {e}")
                else:
                    logger.debug(f"Output file {filename} not found in current directory")

            except Exception as e:
                failed_files.append((filename, str(e)))
                logger.error(f"Error processing file {filename}: {e}")

        if moved_files:
            logger.info(f"Successfully moved files: {moved_files}")
        if failed_files:
            logger.warning(f"Failed to move files: {failed_files}")

    except Exception as e:
        logger.error(f"Critical error in move_roshambo_outputs: {e}")

def cleanup_old_files(working_dir, max_age_hours=24):
    """Clean up old temporary files to prevent disk space issues."""
    try:
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        if not os.path.exists(working_dir):
            return

        cleaned_files = []
        failed_cleanups = []

        for root, dirs, files in os.walk(working_dir):
            for file in files:
                try:
                    file_path = os.path.join(root, file)
                    file_age = current_time - os.path.getmtime(file_path)

                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        cleaned_files.append(file_path)

                except Exception as e:
                    failed_cleanups.append((file_path, str(e)))

            # Remove empty directories
            for dir_name in dirs:
                try:
                    dir_path = os.path.join(root, dir_name)
                    if not os.listdir(dir_path):  # Directory is empty
                        os.rmdir(dir_path)
                        cleaned_files.append(dir_path)
                except Exception as e:
                    failed_cleanups.append((dir_path, str(e)))

        if cleaned_files:
            logger.info(f"Cleaned up {len(cleaned_files)} old files/directories")
        if failed_cleanups:
            logger.warning(f"Failed to clean up {len(failed_cleanups)} items")

    except Exception as e:
        logger.error(f"Error in cleanup_old_files: {e}")

@app.before_request
def before_request():
    """Perform cleanup before each request to manage resources."""
    try:
        # Clean up old files periodically
        cleanup_old_files(ROSHAMBO_WORKING_DIR, max_age_hours=6)
    except Exception as e:
        logger.warning(f"Error in before_request cleanup: {e}")

if __name__ == '__main__':
    print("🧬 Starting Roshambo Flask API Server")
    print("=" * 50)

    # Check GPU availability and show preferred GPU
    try:
        preferred_gpu, gpu_count = check_gpu_availability()
        print(f"🎯 Preferred GPU: {preferred_gpu} (out of {gpu_count} available)")
    except Exception as e:
        print(f"⚠️  GPU check failed: {e}")

    # Check roshambo availability
    try:
        from roshambo.api import get_similarity_scores
        print("✅ Roshambo API available")
    except ImportError as e:
        print(f"❌ Roshambo API not available: {e}")
        print("Please ensure roshambo is installed in your conda environment")

    # Create inpdata directory
    try:
        Path(ROSHAMBO_WORKING_DIR).mkdir(parents=True, exist_ok=True)
        print(f"📁 Working directory: {ROSHAMBO_WORKING_DIR}")
    except Exception as e:
        print(f"❌ Failed to create working directory: {e}")

    # Initial cleanup
    try:
        cleanup_old_files(ROSHAMBO_WORKING_DIR, max_age_hours=1)
        print("🧹 Cleaned up old files")
    except Exception as e:
        print(f"⚠️  Cleanup failed: {e}")

    print("🚀 Starting Flask server on 127.0.0.1:5000")
    print("📋 Available endpoints:")
    print("  GET  /health     - Health check with GPU status")
    print("  POST /similarity - Calculate molecular similarity")
    print("=" * 50)

    # Run the Flask app (localhost only for security)
    try:
        app.run(host='127.0.0.1', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Failed to start Flask server: {e}")
        logger.error(f"Failed to start Flask server: {e}")
